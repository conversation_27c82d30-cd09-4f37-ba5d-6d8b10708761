import { getQueryParam } from './utils/params.util';
import { BaseComponent } from './base';

export class CardTypeFilterComponent extends BaseComponent {
  constructor() {
    super();
    this.init();
  }

  init() {
    this.$cardType = $('[data-promotion-hub-card-type]');
    const params = getQueryParam('card-types');
    if (!params || !this.$cardType.length) {
      return;
    }

    const paramSet = new Set(params.split(','));
    this.filterCards(paramSet);
  }

  filterCards(paramSet) {
    this.$cardType.each(function () {
      const $card = $(this);
      const types = $card.data('promotion-hub-card-type');

      if (!types) {
        $card.remove();
        return;
      }

      const typeArr = types.split(',');
      const isMatched = typeArr.some(
        (slug) =>
          paramSet.has(`${slug}`) || paramSet.has(`the-tin-dung/${slug}`) || paramSet.has(`the-thanh-toan/${slug}`),
      );

      if (!isMatched) {
        $card.remove();
      } else {
        $card.css('display', 'flex');
      }
    });
  }
}
